#include <msp430.h>

/*
 * MSP430F5507 控制两个MG996R舵机例程
 * 使用Timer A0产生PWM信号
 * P1.2 - 舵机1 PWM输出 (TA0.1)
 * P1.3 - 舵机2 PWM输出 (TA0.2)
 * 
 * MG996R舵机参数:
 * - 工作电压: 4.8V-7.2V
 * - PWM周期: 20ms (50Hz)
 * - 脉宽范围: 1ms-2ms (对应0°-180°)
 * - 中位脉宽: 1.5ms (对应90°)
 */

// 系统时钟频率 (Hz)
#define SYSTEM_CLOCK    1000000UL   // 1MHz

// PWM参数定义
#define PWM_PERIOD      20000       // 20ms周期 (50Hz)
#define SERVO_MIN       1000        // 1ms脉宽 (0度)
#define SERVO_MAX       2000        // 2ms脉宽 (180度)
#define SERVO_CENTER    1500        // 1.5ms脉宽 (90度)

// 舵机角度转换宏 (角度转换为脉宽微秒)
#define ANGLE_TO_PULSE(angle) (SERVO_MIN + ((angle) * (SERVO_MAX - SERVO_MIN) / 180))

// 全局变量
volatile unsigned int servo1_angle = 90;   // 舵机1角度
volatile unsigned int servo2_angle = 90;   // 舵机2角度
volatile unsigned int demo_step = 0;       // 演示步骤

// 函数声明
void init_clock(void);
void init_gpio(void);
void init_timer_pwm(void);
void set_servo_angle(unsigned char servo_num, unsigned int angle);
void delay_ms(unsigned int ms);

int main(void)
{
    WDTCTL = WDTPW | WDTHOLD;   // 停止看门狗定时器
    
    // 系统初始化
    init_clock();
    init_gpio();
    init_timer_pwm();
    
    __enable_interrupt();       // 使能全局中断
    
    // 初始化舵机到中位
    set_servo_angle(1, 90);
    set_servo_angle(2, 90);
    delay_ms(1000);
    
    // 主循环 - 舵机演示程序
    while(1)
    {
        switch(demo_step)
        {
            case 0:
                // 两个舵机同时转到0度
                set_servo_angle(1, 0);
                set_servo_angle(2, 0);
                delay_ms(1000);
                demo_step++;
                break;
                
            case 1:
                // 两个舵机同时转到180度
                set_servo_angle(1, 180);
                set_servo_angle(2, 180);
                delay_ms(1000);
                demo_step++;
                break;
                
            case 2:
                // 舵机1转到0度，舵机2转到180度
                set_servo_angle(1, 0);
                set_servo_angle(2, 180);
                delay_ms(1000);
                demo_step++;
                break;
                
            case 3:
                // 舵机1转到180度，舵机2转到0度
                set_servo_angle(1, 180);
                set_servo_angle(2, 0);
                delay_ms(1000);
                demo_step++;
                break;
                
            case 4:
                // 回到中位
                set_servo_angle(1, 90);
                set_servo_angle(2, 90);
                delay_ms(1000);
                demo_step++;
                break;
                
            case 5:
                // 连续扫描 - 舵机1
                for(int angle = 0; angle <= 180; angle += 10)
                {
                    set_servo_angle(1, angle);
                    delay_ms(100);
                }
                for(int angle = 180; angle >= 0; angle -= 10)
                {
                    set_servo_angle(1, angle);
                    delay_ms(100);
                }
                demo_step++;
                break;
                
            case 6:
                // 连续扫描 - 舵机2
                for(int angle = 0; angle <= 180; angle += 10)
                {
                    set_servo_angle(2, angle);
                    delay_ms(100);
                }
                for(int angle = 180; angle >= 0; angle -= 10)
                {
                    set_servo_angle(2, angle);
                    delay_ms(100);
                }
                demo_step = 0;  // 重新开始演示
                break;
                
            default:
                demo_step = 0;
                break;
        }
    }
}

/*
 * 初始化系统时钟
 * 设置MCLK = 1MHz
 */
void init_clock(void)
{
    // 配置DCO为1MHz
    UCSCTL3 |= SELREF_2;                      // 设置DCO参考为REFOCLK
    UCSCTL4 |= SELA_2;                        // 设置ACLK源为REFOCLK
    
    __bis_SR_register(SCG0);                  // 禁用FLL
    UCSCTL0 = 0x0000;                         // 设置最低可能的DCOx, MODx
    UCSCTL1 = DCORSEL_3;                      // 选择DCO范围
    UCSCTL2 = FLLD_1 + 30;                    // 设置DCO倍频器为32 (32*32768 ≈ 1MHz)
    __bic_SR_register(SCG0);                  // 使能FLL
    
    // 等待时钟稳定
    __delay_cycles(250000);
}

/*
 * 初始化GPIO
 * P1.2 - TA0.1 (舵机1 PWM输出)
 * P1.3 - TA0.2 (舵机2 PWM输出)
 */
void init_gpio(void)
{
    // 配置P1.2和P1.3为Timer A0输出
    P1DIR |= BIT2 + BIT3;                     // 设置P1.2, P1.3为输出
    P1SEL |= BIT2 + BIT3;                     // 选择P1.2, P1.3为Timer A0功能
    
    // 其他未使用的引脚设置为输出低电平以降低功耗
    P1OUT = 0x00;
    P1DIR = 0xFF;
    P2OUT = 0x00;
    P2DIR = 0xFF;
    P3OUT = 0x00;
    P3DIR = 0xFF;
    P4OUT = 0x00;
    P4DIR = 0xFF;
    P5OUT = 0x00;
    P5DIR = 0xFF;
    P6OUT = 0x00;
    P6DIR = 0xFF;
    
    // 恢复P1.2和P1.3的设置
    P1DIR |= BIT2 + BIT3;
    P1SEL |= BIT2 + BIT3;
}

/*
 * 初始化Timer A0 PWM
 * 产生20ms周期的PWM信号
 */
void init_timer_pwm(void)
{
    // 停止定时器
    TA0CTL = TASSEL_2 + MC_0 + TACLR;         // SMCLK, 停止模式, 清除定时器
    
    // 设置PWM周期 (20ms)
    TA0CCR0 = PWM_PERIOD - 1;                 // PWM周期
    
    // 配置CCR1 (P1.2 - 舵机1)
    TA0CCTL1 = OUTMOD_7;                      // 复位/置位模式
    TA0CCR1 = SERVO_CENTER;                   // 初始脉宽1.5ms (90度)
    
    // 配置CCR2 (P1.3 - 舵机2)
    TA0CCTL2 = OUTMOD_7;                      // 复位/置位模式
    TA0CCR2 = SERVO_CENTER;                   // 初始脉宽1.5ms (90度)
    
    // 启动定时器
    TA0CTL = TASSEL_2 + MC_1 + TACLR;         // SMCLK, 增计数模式, 清除定时器
}

/*
 * 设置舵机角度
 * servo_num: 舵机编号 (1或2)
 * angle: 角度 (0-180度)
 */
void set_servo_angle(unsigned char servo_num, unsigned int angle)
{
    unsigned int pulse_width;
    
    // 限制角度范围
    if(angle > 180) angle = 180;
    
    // 计算脉宽
    pulse_width = ANGLE_TO_PULSE(angle);
    
    // 设置对应的CCR寄存器
    if(servo_num == 1)
    {
        TA0CCR1 = pulse_width;
        servo1_angle = angle;
    }
    else if(servo_num == 2)
    {
        TA0CCR2 = pulse_width;
        servo2_angle = angle;
    }
}

/*
 * 毫秒延时函数
 * ms: 延时毫秒数
 */
void delay_ms(unsigned int ms)
{
    while(ms--)
    {
        __delay_cycles(1000);  // 1MHz时钟下，1000个周期 = 1ms
    }
}
