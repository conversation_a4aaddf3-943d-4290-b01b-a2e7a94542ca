#include "ti_msp_dl_config.h"

/*
 * MSPM0G3507 控制两个MG996R舵机例程
 * 使用Timer G0产生PWM信号
 * PA0 - 舵机1 PWM输出 (TIMG0_C0)
 * PA1 - 舵机2 PWM输出 (TIMG0_C1)
 *
 * MG996R舵机参数:
 * - 工作电压: 4.8V-7.2V
 * - PWM周期: 20ms (50Hz)
 * - 脉宽范围: 1ms-2ms (对应0°-180°)
 * - 中位脉宽: 1.5ms (对应90°)
 */

// 系统时钟频率 (Hz)
#define SYSTEM_CLOCK    32000000UL  // 32MHz

// PWM参数定义
#define PWM_FREQUENCY   50          // 50Hz
#define PWM_PERIOD      (SYSTEM_CLOCK / PWM_FREQUENCY)  // PWM周期计数值
#define SERVO_MIN_US    1000        // 1ms脉宽 (0度)
#define SERVO_MAX_US    2000        // 2ms脉宽 (180度)
#define SERVO_CENTER_US 1500        // 1.5ms脉宽 (90度)

// 微秒转换为计数值的宏
#define US_TO_TICKS(us) ((us) * (SYSTEM_CLOCK / 1000000))

// 舵机角度转换宏 (角度转换为脉宽微秒)
#define ANGLE_TO_PULSE_US(angle) (SERVO_MIN_US + ((angle) * (SERVO_MAX_US - SERVO_MIN_US) / 180))

// 全局变量
volatile unsigned int servo1_angle = 90;   // 舵机1角度
volatile unsigned int servo2_angle = 90;   // 舵机2角度
volatile unsigned int demo_step = 0;       // 演示步骤

// 函数声明
void init_system(void);
void set_servo_angle(unsigned char servo_num, unsigned int angle);
void delay_ms(unsigned int ms);

int main(void)
{
    // 系统初始化
    init_system();

    // 初始化舵机到中位
    set_servo_angle(1, 90);
    set_servo_angle(2, 90);
    delay_ms(1000);

    // 主循环 - 舵机演示程序
    while(1)
    {
        switch(demo_step)
        {
            case 0:
                // 两个舵机同时转到0度
                set_servo_angle(1, 0);
                set_servo_angle(2, 0);
                delay_ms(1000);
                demo_step++;
                break;

            case 1:
                // 两个舵机同时转到180度
                set_servo_angle(1, 180);
                set_servo_angle(2, 180);
                delay_ms(1000);
                demo_step++;
                break;

            case 2:
                // 舵机1转到0度，舵机2转到180度
                set_servo_angle(1, 0);
                set_servo_angle(2, 180);
                delay_ms(1000);
                demo_step++;
                break;

            case 3:
                // 舵机1转到180度，舵机2转到0度
                set_servo_angle(1, 180);
                set_servo_angle(2, 0);
                delay_ms(1000);
                demo_step++;
                break;

            case 4:
                // 回到中位
                set_servo_angle(1, 90);
                set_servo_angle(2, 90);
                delay_ms(1000);
                demo_step++;
                break;

            case 5:
                // 连续扫描 - 舵机1
                for(int angle = 0; angle <= 180; angle += 10)
                {
                    set_servo_angle(1, angle);
                    delay_ms(100);
                }
                for(int angle = 180; angle >= 0; angle -= 10)
                {
                    set_servo_angle(1, angle);
                    delay_ms(100);
                }
                demo_step++;
                break;

            case 6:
                // 连续扫描 - 舵机2
                for(int angle = 0; angle <= 180; angle += 10)
                {
                    set_servo_angle(2, angle);
                    delay_ms(100);
                }
                for(int angle = 180; angle >= 0; angle -= 10)
                {
                    set_servo_angle(2, angle);
                    delay_ms(100);
                }
                demo_step = 0;  // 重新开始演示
                break;

            default:
                demo_step = 0;
                break;
        }
    }
}

/*
 * 系统初始化
 * 包括时钟、GPIO和PWM初始化
 */
void init_system(void)
{
    // 调用系统配置函数 (由SysConfig生成)
    SYSCFG_DL_init();

    // 启动Timer G0
    DL_TimerG_startCounter(PWM_0_INST);
}

/*
 * 设置舵机角度
 * servo_num: 舵机编号 (1或2)
 * angle: 角度 (0-180度)
 */
void set_servo_angle(unsigned char servo_num, unsigned int angle)
{
    unsigned int pulse_width_us;
    unsigned int pulse_width_ticks;

    // 限制角度范围
    if(angle > 180) angle = 180;

    // 计算脉宽 (微秒)
    pulse_width_us = ANGLE_TO_PULSE_US(angle);

    // 转换为定时器计数值
    pulse_width_ticks = US_TO_TICKS(pulse_width_us);

    // 设置对应的比较寄存器
    if(servo_num == 1)
    {
        DL_TimerG_setCaptureCompareValue(PWM_0_INST, pulse_width_ticks, DL_TIMER_CC_0_INDEX);
        servo1_angle = angle;
    }
    else if(servo_num == 2)
    {
        DL_TimerG_setCaptureCompareValue(PWM_0_INST, pulse_width_ticks, DL_TIMER_CC_1_INDEX);
        servo2_angle = angle;
    }
}

/*
 * 毫秒延时函数
 * ms: 延时毫秒数
 */
void delay_ms(unsigned int ms)
{
    while(ms--)
    {
        delay_cycles(32000);  // 32MHz时钟下，32000个周期 = 1ms
    }
}
