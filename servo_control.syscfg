/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.18.0+3266"}
 */

/**
 * Import the modules used in this configuration.
 */
const PWM   = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1  = PWM.addInstance();
const PWM2  = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");

/**
 * Write custom configuration values to the imported modules.
 */
PWM1.$name                      = "PWM_0";
PWM1.clockDivider               = 8;
PWM1.clockPrescale              = 256;
PWM1.timerCount                 = 12500;
PWM1.ccIndex                    = [0,1];
PWM1.peripheral.$assign         = "TIMG0";
PWM1.peripheral.ccp0Pin.$assign = "PA0";
PWM1.peripheral.ccp1Pin.$assign = "PA1";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle    = 7.5;
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.dutyCycle    = 7.5;

SYSCTL.clockTreeEn = true;
