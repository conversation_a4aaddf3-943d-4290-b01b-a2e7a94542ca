# MSPM0G3507 双舵机控制项目

## 🎯 项目概述

本项目为MSPM0G3507微控制器控制两个MG996R舵机的完整例程，适用于Code Composer Studio (CCS)开发环境。

## 📁 文件结构

```
MSPM0G3507_Servo_Control/
├── main.c                    # 主程序 - 完整功能演示
├── servo_test.c              # 测试程序 - 简化版本
├── servo_control.syscfg      # SysConfig配置文件
├── ti_msp_dl_config.h        # 系统配置头文件
├── ti_msp_dl_config.c        # 系统配置源文件
├── mspm0g3507.lds           # 链接器脚本
├── Makefile                 # 命令行编译支持
├── README.md                # 详细使用说明
├── 项目说明.md              # 本文件
├── .project                 # CCS项目配置
└── .cproject               # CCS编译配置
```

## 🔌 硬件连接

### MSPM0G3507 LaunchPad连接
```
MSPM0G3507          MG996R舵机1         MG996R舵机2
PA0 (Pin 33)   →    PWM信号线(橙)       
PA1 (Pin 34)   →                       PWM信号线(橙)
5V外部电源     →    电源线(红)          电源线(红)
GND           →    地线(棕)            地线(棕)
```

### ⚠️ 重要提醒
- **必须使用外部5V电源**：MG996R舵机工作电流可达900mA，MSPM0G3507无法提供
- **共地连接**：确保舵机电源地线与MSPM0G3507的GND相连
- **电源容量**：推荐使用至少2A的5V电源适配器

## 🚀 快速开始

### 1. 环境准备
- 安装Code Composer Studio (最新版本)
- 安装MSPM0 SDK
- 准备MSPM0G3507 LaunchPad开发板
- 准备两个MG996R舵机和5V电源

### 2. 导入项目
1. 打开CCS
2. File → Import → CCS Projects
3. 选择项目文件夹导入

### 3. 配置和编译
1. 双击`servo_control.syscfg`查看/修改配置
2. 右键项目 → Build Project
3. 连接开发板
4. 右键项目 → Debug As → CCS Debug

### 4. 程序选择
- **main.c**: 完整演示程序，包含多种运动模式
- **servo_test.c**: 简化测试程序，适合初次调试

## 🎮 程序功能

### 主程序演示模式
1. **同步运动**: 两舵机同时0°→180°
2. **对称运动**: 舵机1和舵机2反向运动
3. **回中位**: 两舵机回到90°中位
4. **连续扫描**: 舵机1和舵机2分别扫描
5. **双舵机同步扫描**: 反向同步扫描

### 测试程序功能
- 基本位置测试（0°、90°、180°）
- 单舵机扫描测试
- 双舵机同步测试

## ⚙️ 技术参数

### MSPM0G3507配置
- **系统时钟**: 32MHz
- **PWM频率**: 50Hz (20ms周期)
- **Timer**: Timer G0
- **分频**: 8 × 256 = 2048
- **计数周期**: 12500

### MG996R舵机规格
- **工作电压**: 4.8V - 7.2V
- **工作电流**: 100mA - 900mA
- **扭矩**: 9.4kg·cm (4.8V), 11kg·cm (6V)
- **控制信号**: PWM (1ms-2ms脉宽)

## 🔧 自定义开发

### 角度控制API
```c
// 设置舵机角度 (0-180度)
set_servo_angle(1, 45);    // 舵机1转到45度
set_servo_angle(2, 135);   // 舵机2转到135度
```

### PWM参数修改
在`servo_control.syscfg`中可以调整：
- PWM频率
- 时钟分频
- 占空比范围

### 扩展更多舵机
可以使用以下引脚扩展：
- PA2 → Timer G0 CC2
- PA3 → Timer G0 CC3
- 或使用Timer G1的其他通道

## 🐛 故障排除

### 常见问题
1. **舵机不动作**
   - 检查5V电源连接
   - 确认PWM信号线连接正确
   - 验证舵机是否损坏

2. **舵机抖动**
   - 检查电源稳定性
   - 确认电源容量足够
   - 检查接线是否牢固

3. **编译错误**
   - 确认安装了MSPM0 SDK
   - 检查CCS版本兼容性
   - 验证项目配置正确

4. **下载失败**
   - 检查调试器连接
   - 确认目标器件选择
   - 尝试擦除Flash重新下载

### 调试技巧
- 使用示波器观察PA0、PA1的PWM波形
- 正常PWM：周期20ms，脉宽1-2ms
- 可以先运行`servo_test.c`验证基本功能

## 📈 性能优化

### 功耗优化
- 未使用GPIO设置为低功耗模式
- 可以使用低功耗模式在延时期间

### 精度优化
- 可以增加PWM分辨率
- 实现软件滤波减少抖动
- 添加位置反馈闭环控制

## 🔄 版本历史

- **v1.0**: 初始版本，支持双舵机基本控制
- 基于MSPM0 SDK 1.40.01.03
- 支持CCS 12.x及以上版本

## 📞 技术支持

如遇到问题，请检查：
1. 硬件连接是否正确
2. 电源是否充足稳定
3. 软件配置是否匹配
4. SDK和工具链版本是否兼容

## 📄 许可证

本项目基于TI的示例代码开发，遵循相应的开源许可证。
