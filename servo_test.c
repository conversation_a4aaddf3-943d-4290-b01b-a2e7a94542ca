#include <msp430.h>

/*
 * MSP430F5507 双舵机测试程序 - 简化版
 * 用于快速测试舵机连接和基本功能
 * 
 * 连接方式:
 * P1.2 → 舵机1 PWM信号 (橙色线)
 * P1.3 → 舵机2 PWM信号 (橙色线)
 * 5V   → 舵机电源 (红色线)
 * GND  → 舵机地线 (棕色线)
 */

// PWM参数
#define PWM_PERIOD      20000       // 20ms周期
#define SERVO_0_DEG     1000        // 0度对应1ms脉宽
#define SERVO_90_DEG    1500        // 90度对应1.5ms脉宽  
#define SERVO_180_DEG   2000        // 180度对应2ms脉宽

void init_system(void);
void test_servo_basic(void);
void test_servo_sweep(void);
void set_servo1_angle(unsigned int angle);
void set_servo2_angle(unsigned int angle);
void delay_ms(unsigned int ms);

int main(void)
{
    WDTCTL = WDTPW | WDTHOLD;   // 停止看门狗
    
    init_system();              // 系统初始化
    
    while(1)
    {
        test_servo_basic();     // 基本位置测试
        delay_ms(2000);
        
        test_servo_sweep();     // 扫描测试
        delay_ms(2000);
    }
}

/*
 * 系统初始化
 */
void init_system(void)
{
    // 时钟初始化 - 使用1MHz DCO
    UCSCTL3 |= SELREF_2;        // DCO参考REFOCLK
    UCSCTL4 |= SELA_2;          // ACLK = REFOCLK
    
    __bis_SR_register(SCG0);    // 禁用FLL
    UCSCTL0 = 0x0000;
    UCSCTL1 = DCORSEL_3;        // DCO范围选择
    UCSCTL2 = FLLD_1 + 30;      // DCO倍频 (约1MHz)
    __bic_SR_register(SCG0);    // 使能FLL
    
    __delay_cycles(250000);     // 等待时钟稳定
    
    // GPIO初始化
    P1DIR |= BIT2 + BIT3;       // P1.2, P1.3设为输出
    P1SEL |= BIT2 + BIT3;       // 选择Timer功能
    P1OUT &= ~(BIT2 + BIT3);    // 初始输出低
    
    // Timer A0 PWM初始化
    TA0CTL = TASSEL_2 + MC_0 + TACLR;   // SMCLK, 停止, 清除
    TA0CCR0 = PWM_PERIOD - 1;           // 设置周期
    
    // CCR1配置 (P1.2 - 舵机1)
    TA0CCTL1 = OUTMOD_7;                // 复位/置位模式
    TA0CCR1 = SERVO_90_DEG;             // 初始90度
    
    // CCR2配置 (P1.3 - 舵机2)  
    TA0CCTL2 = OUTMOD_7;                // 复位/置位模式
    TA0CCR2 = SERVO_90_DEG;             // 初始90度
    
    // 启动Timer
    TA0CTL = TASSEL_2 + MC_1 + TACLR;   // SMCLK, 增计数模式
    
    __enable_interrupt();               // 使能中断
}

/*
 * 基本位置测试
 * 测试0度、90度、180度三个基本位置
 */
void test_servo_basic(void)
{
    // 测试0度位置
    set_servo1_angle(0);
    set_servo2_angle(0);
    delay_ms(1000);
    
    // 测试90度位置
    set_servo1_angle(90);
    set_servo2_angle(90);
    delay_ms(1000);
    
    // 测试180度位置
    set_servo1_angle(180);
    set_servo2_angle(180);
    delay_ms(1000);
    
    // 测试不同角度组合
    set_servo1_angle(45);
    set_servo2_angle(135);
    delay_ms(1000);
    
    set_servo1_angle(135);
    set_servo2_angle(45);
    delay_ms(1000);
}

/*
 * 扫描测试
 * 舵机连续扫描运动
 */
void test_servo_sweep(void)
{
    int angle;
    
    // 舵机1扫描，舵机2固定
    set_servo2_angle(90);
    for(angle = 0; angle <= 180; angle += 20)
    {
        set_servo1_angle(angle);
        delay_ms(200);
    }
    for(angle = 180; angle >= 0; angle -= 20)
    {
        set_servo1_angle(angle);
        delay_ms(200);
    }
    
    // 舵机2扫描，舵机1固定
    set_servo1_angle(90);
    for(angle = 0; angle <= 180; angle += 20)
    {
        set_servo2_angle(angle);
        delay_ms(200);
    }
    for(angle = 180; angle >= 0; angle -= 20)
    {
        set_servo2_angle(angle);
        delay_ms(200);
    }
    
    // 双舵机同步扫描
    for(angle = 0; angle <= 180; angle += 15)
    {
        set_servo1_angle(angle);
        set_servo2_angle(180 - angle);  // 反向运动
        delay_ms(150);
    }
    for(angle = 180; angle >= 0; angle -= 15)
    {
        set_servo1_angle(angle);
        set_servo2_angle(180 - angle);
        delay_ms(150);
    }
}

/*
 * 设置舵机1角度
 * angle: 0-180度
 */
void set_servo1_angle(unsigned int angle)
{
    unsigned int pulse_width;
    
    if(angle > 180) angle = 180;
    
    // 角度转换为脉宽 (线性插值)
    pulse_width = SERVO_0_DEG + (angle * (SERVO_180_DEG - SERVO_0_DEG) / 180);
    
    TA0CCR1 = pulse_width;
}

/*
 * 设置舵机2角度  
 * angle: 0-180度
 */
void set_servo2_angle(unsigned int angle)
{
    unsigned int pulse_width;
    
    if(angle > 180) angle = 180;
    
    // 角度转换为脉宽 (线性插值)
    pulse_width = SERVO_0_DEG + (angle * (SERVO_180_DEG - SERVO_0_DEG) / 180);
    
    TA0CCR2 = pulse_width;
}

/*
 * 毫秒延时
 * ms: 延时毫秒数
 */
void delay_ms(unsigned int ms)
{
    while(ms--)
    {
        __delay_cycles(1000);   // 1MHz时钟，1000周期=1ms
    }
}
