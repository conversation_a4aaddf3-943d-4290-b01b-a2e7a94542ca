#include "ti_msp_dl_config.h"

/*
 * MSPM0G3507 双舵机测试程序 - 简化版
 * 用于快速测试舵机连接和基本功能
 *
 * 连接方式:
 * PA0 → 舵机1 PWM信号 (橙色线)
 * PA1 → 舵机2 PWM信号 (橙色线)
 * 5V  → 舵机电源 (红色线)
 * GND → 舵机地线 (棕色线)
 */

// 系统时钟频率
#define SYSTEM_CLOCK    32000000UL  // 32MHz

// PWM参数 (微秒)
#define SERVO_0_DEG_US     1000     // 0度对应1ms脉宽
#define SERVO_90_DEG_US    1500     // 90度对应1.5ms脉宽
#define SERVO_180_DEG_US   2000     // 180度对应2ms脉宽

// 微秒转换为计数值
#define US_TO_TICKS(us) ((us) * (SYSTEM_CLOCK / 1000000))

void init_system(void);
void test_servo_basic(void);
void test_servo_sweep(void);
void set_servo1_angle(unsigned int angle);
void set_servo2_angle(unsigned int angle);
void delay_ms(unsigned int ms);

int main(void)
{
    init_system();              // 系统初始化

    while(1)
    {
        test_servo_basic();     // 基本位置测试
        delay_ms(2000);

        test_servo_sweep();     // 扫描测试
        delay_ms(2000);
    }
}

/*
 * 系统初始化
 */
void init_system(void)
{
    // 调用系统配置函数 (由SysConfig生成)
    SYSCFG_DL_init();

    // 启动Timer G0
    DL_TimerG_startCounter(PWM_0_INST);

    // 初始化舵机到90度中位
    set_servo1_angle(90);
    set_servo2_angle(90);
}

/*
 * 基本位置测试
 * 测试0度、90度、180度三个基本位置
 */
void test_servo_basic(void)
{
    // 测试0度位置
    set_servo1_angle(0);
    set_servo2_angle(0);
    delay_ms(1000);
    
    // 测试90度位置
    set_servo1_angle(90);
    set_servo2_angle(90);
    delay_ms(1000);
    
    // 测试180度位置
    set_servo1_angle(180);
    set_servo2_angle(180);
    delay_ms(1000);
    
    // 测试不同角度组合
    set_servo1_angle(45);
    set_servo2_angle(135);
    delay_ms(1000);
    
    set_servo1_angle(135);
    set_servo2_angle(45);
    delay_ms(1000);
}

/*
 * 扫描测试
 * 舵机连续扫描运动
 */
void test_servo_sweep(void)
{
    int angle;
    
    // 舵机1扫描，舵机2固定
    set_servo2_angle(90);
    for(angle = 0; angle <= 180; angle += 20)
    {
        set_servo1_angle(angle);
        delay_ms(200);
    }
    for(angle = 180; angle >= 0; angle -= 20)
    {
        set_servo1_angle(angle);
        delay_ms(200);
    }
    
    // 舵机2扫描，舵机1固定
    set_servo1_angle(90);
    for(angle = 0; angle <= 180; angle += 20)
    {
        set_servo2_angle(angle);
        delay_ms(200);
    }
    for(angle = 180; angle >= 0; angle -= 20)
    {
        set_servo2_angle(angle);
        delay_ms(200);
    }
    
    // 双舵机同步扫描
    for(angle = 0; angle <= 180; angle += 15)
    {
        set_servo1_angle(angle);
        set_servo2_angle(180 - angle);  // 反向运动
        delay_ms(150);
    }
    for(angle = 180; angle >= 0; angle -= 15)
    {
        set_servo1_angle(angle);
        set_servo2_angle(180 - angle);
        delay_ms(150);
    }
}

/*
 * 设置舵机1角度
 * angle: 0-180度
 */
void set_servo1_angle(unsigned int angle)
{
    unsigned int pulse_width_us;
    unsigned int pulse_width_ticks;

    if(angle > 180) angle = 180;

    // 角度转换为脉宽微秒 (线性插值)
    pulse_width_us = SERVO_0_DEG_US + (angle * (SERVO_180_DEG_US - SERVO_0_DEG_US) / 180);

    // 转换为定时器计数值
    pulse_width_ticks = US_TO_TICKS(pulse_width_us);

    // 设置PWM占空比
    DL_TimerG_setCaptureCompareValue(PWM_0_INST, pulse_width_ticks, DL_TIMER_CC_0_INDEX);
}

/*
 * 设置舵机2角度
 * angle: 0-180度
 */
void set_servo2_angle(unsigned int angle)
{
    unsigned int pulse_width_us;
    unsigned int pulse_width_ticks;

    if(angle > 180) angle = 180;

    // 角度转换为脉宽微秒 (线性插值)
    pulse_width_us = SERVO_0_DEG_US + (angle * (SERVO_180_DEG_US - SERVO_0_DEG_US) / 180);

    // 转换为定时器计数值
    pulse_width_ticks = US_TO_TICKS(pulse_width_us);

    // 设置PWM占空比
    DL_TimerG_setCaptureCompareValue(PWM_0_INST, pulse_width_ticks, DL_TIMER_CC_1_INDEX);
}

/*
 * 毫秒延时
 * ms: 延时毫秒数
 */
void delay_ms(unsigned int ms)
{
    while(ms--)
    {
        delay_cycles(32000);   // 32MHz时钟，32000周期=1ms
    }
}
