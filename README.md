# MSPM0G3507 双舵机控制例程

本项目演示如何使用MSPM0G3507微控制器控制两个MG996R舵机。

## 硬件连接

### MSPM0G3507引脚连接
- **PB16** → 舵机1 PWM信号线 (橙色/黄色线) - LaunchPad Pin 7
- **PB17** → 舵机2 PWM信号线 (橙色/黄色线) - LaunchPad Pin 8
- **VCC** → 舵机电源正极 (红色线) - 需要外部5V电源
- **GND** → 舵机电源负极 (棕色/黑色线) - 与MSPM0G3507共地

### MG996R舵机规格
- 工作电压: 4.8V - 7.2V (推荐5V)
- 工作电流: 100mA - 900mA (负载相关)
- 扭矩: 9.4kg·cm (4.8V), 11kg·cm (6V)
- 转动角度: 180° (±90°)
- PWM控制信号:
  - 周期: 20ms (50Hz)
  - 脉宽: 1ms-2ms (对应0°-180°)
  - 中位: 1.5ms (对应90°)

## 软件功能

### 主要特性
1. **双舵机独立控制**: 可以同时控制两个舵机到不同角度
2. **角度精确控制**: 支持0°-180°范围内的精确角度控制
3. **演示程序**: 内置多种运动模式演示
4. **低功耗设计**: 未使用的GPIO设置为低功耗模式

### 演示模式
程序包含以下演示模式，循环执行：

1. **同步运动**: 两个舵机同时转到0°，然后同时转到180°
2. **对称运动**: 一个舵机转到0°，另一个转到180°，然后交换
3. **回中位**: 两个舵机都回到90°中位
4. **连续扫描**: 舵机1和舵机2分别进行0°-180°的连续扫描

### 核心函数
- `set_servo_angle(servo_num, angle)`: 设置指定舵机的角度
- `init_system()`: 初始化系统时钟、GPIO和PWM
- `SYSCFG_DL_init()`: SysConfig生成的系统配置函数
- `delay_ms(ms)`: 毫秒级延时函数

## 使用方法

### 在CCS中打开项目
1. 打开Code Composer Studio (CCS) - 需要支持MSPM0系列
2. 选择 File → Import → CCS Projects
3. 浏览到项目文件夹，选择导入
4. 确保已安装MSPM0 SDK

### 配置SysConfig
1. 双击 `servo_control.syscfg` 文件
2. 在SysConfig图形界面中可以调整PWM参数
3. 保存后会自动生成 `ti_msp_dl_config.h` 和 `ti_msp_dl_config.c`

### 编译和下载
1. 右键点击项目名称，选择 "Build Project"
2. 连接MSPM0G3507开发板到电脑
3. 右键点击项目名称，选择 "Debug As" → "CCS Debug"
4. 程序将自动下载到MCU并开始调试

### 自定义控制
如果需要自定义舵机控制，可以修改main()函数中的主循环：

```c
// 示例：控制舵机1转到45度，舵机2转到135度
set_servo_angle(1, 45);
set_servo_angle(2, 135);
delay_ms(1000);
```

## 技术细节

### PWM参数配置
- **系统时钟**: 32MHz
- **PWM频率**: 50Hz (20ms周期)
- **分频器**: 8 × 256 = 2048
- **计数值**: 12500 (对应20ms周期)
- **角度计算**: `脉宽(μs) = 1000 + (角度 × 1000 / 180)`

### Timer G0配置
- **时钟源**: BUSCLK (32MHz)
- **分频**: 预分频8，时钟分频256
- **计数模式**: 向上计数模式
- **PWM模式**: 边沿对齐PWM
- **CC0**: 控制PB16输出 (舵机1)
- **CC1**: 控制PB17输出 (舵机2)

## 注意事项

### 电源要求
- **重要**: MG996R舵机需要外部5V电源供电，MSP430的3.3V输出无法提供足够电流
- 确保舵机电源负极与MSP430的GND连接
- 推荐使用至少2A的5V电源适配器

### 安全提示
- 首次使用时建议先不连接舵机负载，观察运动是否正常
- 避免舵机堵转，可能导致过流损坏
- 长时间不使用时建议断开舵机电源

### 调试建议
- 可以使用示波器观察P1.2和P1.3的PWM波形
- 正常的PWM信号应该是周期20ms，脉宽在1-2ms之间变化
- 如果舵机不动作，检查电源连接和PWM信号

## 扩展功能

### 可能的改进
1. **串口控制**: 添加UART接口，通过串口命令控制舵机角度
2. **传感器反馈**: 添加电位器或编码器获取舵机实际位置
3. **平滑运动**: 实现角度插值，使舵机运动更平滑
4. **多舵机支持**: 扩展到控制更多舵机
5. **PID控制**: 实现闭环位置控制

### 引脚扩展
如需控制更多舵机，可以使用以下引脚：
- P1.4 → TA0.3 (第3个舵机)
- P1.5 → TA0.4 (第4个舵机)
- P2.0 → TA1.1 (使用Timer A1)
- P2.1 → TA1.2 (使用Timer A1)

## 故障排除

### 常见问题
1. **舵机不动作**
   - 检查电源连接 (5V供电)
   - 检查PWM信号线连接
   - 确认舵机没有损坏

2. **舵机抖动**
   - 检查电源是否稳定
   - 确认PWM信号质量
   - 可能是电源纹波过大

3. **编译错误**
   - 确认CCS版本支持MSP430F5507
   - 检查头文件路径设置
   - 确认链接器文件正确

4. **下载失败**
   - 检查仿真器连接
   - 确认目标器件选择正确
   - 尝试擦除Flash后重新下载
