# MSP430F5507 舵机控制项目 Makefile
# 适用于TI MSP430 GCC工具链

# 工具链设置
CC = msp430-gcc
OBJCOPY = msp430-objcopy
SIZE = msp430-size

# 目标MCU
MCU = msp430f5507

# 项目名称
PROJECT = servo_control

# 源文件
SOURCES = main.c
TEST_SOURCES = servo_test.c

# 编译选项
CFLAGS = -mmcu=$(MCU) -O2 -Wall -Wextra
CFLAGS += -ffunction-sections -fdata-sections
CFLAGS += -g -gdwarf-2

# 链接选项
LDFLAGS = -mmcu=$(MCU) -Wl,--gc-sections

# 目标文件
OBJECTS = $(SOURCES:.c=.o)
TEST_OBJECTS = $(TEST_SOURCES:.c=.o)

# 默认目标
all: $(PROJECT).elf $(PROJECT).hex size

# 测试程序
test: servo_test.elf servo_test.hex test_size

# 编译主程序
$(PROJECT).elf: $(OBJECTS)
	$(CC) $(LDFLAGS) -o $@ $^

# 编译测试程序
servo_test.elf: $(TEST_OBJECTS)
	$(CC) $(LDFLAGS) -o $@ $^

# 生成HEX文件
%.hex: %.elf
	$(OBJCOPY) -O ihex $< $@

# 编译C文件
%.o: %.c
	$(CC) $(CFLAGS) -c -o $@ $<

# 显示程序大小
size: $(PROJECT).elf
	@echo "=== 主程序大小信息 ==="
	$(SIZE) $<

test_size: servo_test.elf
	@echo "=== 测试程序大小信息 ==="
	$(SIZE) $<

# 清理
clean:
	rm -f *.o *.elf *.hex *.map

# 帮助信息
help:
	@echo "可用目标:"
	@echo "  all      - 编译主程序 (main.c)"
	@echo "  test     - 编译测试程序 (servo_test.c)"
	@echo "  clean    - 清理编译文件"
	@echo "  size     - 显示主程序大小"
	@echo "  test_size- 显示测试程序大小"
	@echo "  help     - 显示此帮助信息"
	@echo ""
	@echo "使用示例:"
	@echo "  make all     # 编译主程序"
	@echo "  make test    # 编译测试程序"
	@echo "  make clean   # 清理文件"

# 声明伪目标
.PHONY: all test clean size test_size help

# 依赖关系
main.o: main.c
servo_test.o: servo_test.c
