<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.ti.ccstudio.buildDefinitions.MSP430.Debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.ti.ccstudio.buildDefinitions.MSP430.Debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="com.ti.ccstudio.binaryparser.CoffParser" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.CoffErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.AsmErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.LinkErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="out" artifactName="${ProjName}" buildArtefactType="com.ti.ccstudio.buildDefinitions.MSP430.outputType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=com.ti.ccstudio.buildDefinitions.MSP430.outputType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="${CG_CLEAN_CMD}" description="" id="com.ti.ccstudio.buildDefinitions.MSP430.Debug.**********" name="Debug" parent="com.ti.ccstudio.buildDefinitions.MSP430.Debug">
					<folderInfo id="com.ti.ccstudio.buildDefinitions.MSP430.Debug.**********." name="/" resourcePath="">
						<toolChain id="com.ti.ccstudio.buildDefinitions.MSP430.DebugToolchain.**********" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.MSP430.DebugToolchain">
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" valueType="stringList">
								<listOptionValue builtIn="false" value="DEVICE_CONFIGURATION_ID=MSP430F5507"/>
								<listOptionValue builtIn="false" value="DEVICE_CORE_ID=MSP430"/>
								<listOptionValue builtIn="false" value="DEVICE_ENDIANNESS=little"/>
								<listOptionValue builtIn="false" value="OUTPUT_FORMAT=ELF"/>
								<listOptionValue builtIn="false" value="CCS_MBS_VERSION=6.1.3"/>
								<listOptionValue builtIn="false" value="RUNTIME_SUPPORT_LIBRARY=libc.a"/>
								<listOptionValue builtIn="false" value="OUTPUT_TYPE=executable"/>
							</option>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.MSP430.DebugTargetPlatform.**********" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.MSP430.DebugTargetPlatform"/>
							<builder buildPath="${BuildDirectory}" id="com.ti.ccstudio.buildDefinitions.MSP430.DebugBuilder.**********" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU Make" superClass="com.ti.ccstudio.buildDefinitions.MSP430.DebugBuilder"/>
							<tool id="com.ti.ccstudio.buildDefinitions.MSP430.exe.DebugCompiler.**********" name="MSP430 Compiler" superClass="com.ti.ccstudio.buildDefinitions.MSP430.exe.DebugCompiler">
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.SILICON_VERSION" name="Silicon version (--silicon_version, -v)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.SILICON_VERSION" value="msp" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DEBUGGING_MODEL" name="Debugging model" superClass="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DEBUGGING_MODEL" value="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DEBUGGING_MODEL.SYMDEBUG__DWARF" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DIAG_WARNING" name="Treat diagnostic &lt;id&gt; as warning (--diag_warning, -pdsw)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DIAG_WARNING" valueType="stringList">
									<listOptionValue builtIn="false" value="225"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DISPLAY_ERROR_NUMBER" name="Emit diagnostic identifier numbers (--display_error_number, -pden)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DISPLAY_ERROR_NUMBER" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DIAG_WRAP" name="Wrap diagnostic messages (--diag_wrap)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DIAG_WRAP" value="com.ti.ccstudio.buildDefinitions.MSP430.compilerID.DIAG_WRAP.off" valueType="enumerated"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.compiler.inputType__C_SRCS" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.MSP430.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.compiler.inputType__CPP_SRCS" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.MSP430.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.compiler.inputType__ASM_SRCS" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.MSP430.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.compiler.inputType__ASM2_SRCS" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.MSP430.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.MSP430.exe.DebugLinker.**********" name="MSP430 Linker" superClass="com.ti.ccstudio.buildDefinitions.MSP430.exe.DebugLinker">
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.HEAP_SIZE" name="Heap size for C/C++ dynamic memory allocation (--heap_size, -heap)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.HEAP_SIZE" value="160" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.STACK_SIZE" name="Stack size for C/C++ applications (--stack_size, -stack)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.STACK_SIZE" value="160" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.OUTPUT_FILE" name="Specify output file name (--output_file, -o)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.OUTPUT_FILE" value="${ProjName}.out" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.MAP_FILE" name="Link information (map) listed into &lt;file&gt; (--map_file, -m)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.MAP_FILE" value="${ProjName}.map" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.XML_LINK_INFO" name="Detailed link information data-base into &lt;file&gt; (--xml_link_info, -xml_link_info)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.XML_LINK_INFO" value="${ProjName}_linkInfo.xml" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.DIAG_WRAP" name="Wrap diagnostic messages (--diag_wrap)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.DIAG_WRAP" value="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.DIAG_WRAP.off" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.DISPLAY_ERROR_NUMBER" name="Emit diagnostic identifier numbers (--display_error_number)" superClass="com.ti.ccstudio.buildDefinitions.MSP430.linkerID.DISPLAY_ERROR_NUMBER" value="true" valueType="boolean"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__CMD_SRCS" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__CMD_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__C_SRCS" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__LIB_SRCS" name="Library Sources" superClass="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__LIB_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__OBJ_SRCS" name="Object Sources" superClass="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__OBJ_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__ASM_SRCS" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__ASM2_SRCS" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.MSP430.exeLinker.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.MSP430.hex.DebugHex.**********" name="MSP430 Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.MSP430.hex.DebugHex"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="MSP430_Servo_Control.com.ti.ccstudio.buildDefinitions.MSP430.ProjectType.**********" name="MSP430" projectType="com.ti.ccstudio.buildDefinitions.MSP430.ProjectType"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration"/>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="refreshScope"/>
</cproject>
